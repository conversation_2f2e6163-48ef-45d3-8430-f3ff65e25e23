<view class="health-container"><view class="nav-bar"><view class="nav-left"><text class="nav-title">健康数据</text></view><view class="nav-right"><view class="nav-icon" bindtap="{{a}}"><text class="iconfont icon-settings">⚙️</text></view></view></view><scroll-view class="main-content" scroll-y="true"><view class="time-selector"><view class="{{['tab-item', b && 'active']}}" bindtap="{{c}}"><text>今天</text></view><view class="{{['tab-item', d && 'active']}}" bindtap="{{e}}"><text>本周</text></view><view class="{{['tab-item', f && 'active']}}" bindtap="{{g}}"><text>本月</text></view></view><view class="metrics-grid"><view class="metric-card heart-rate"><view class="metric-header"><text class="metric-icon">❤️</text><text class="metric-label">心率</text></view><text class="metric-value">72 <text class="metric-unit">bpm</text></text><view class="metric-trend"><text class="trend-text up">↑ 2% 较昨日</text></view><view class="metric-status normal"><text>正常范围</text></view></view><view class="metric-card blood-pressure"><view class="metric-header"><text class="metric-icon">🩸</text><text class="metric-label">血压</text></view><text class="metric-value">125/80 <text class="metric-unit">mmHg</text></text><view class="metric-trend"><text class="trend-text stable">稳定</text></view><view class="metric-status normal"><text>正常范围</text></view></view><view class="metric-card blood-oxygen"><view class="metric-header"><text class="metric-icon">🫁</text><text class="metric-label">血氧</text></view><text class="metric-value">98 <text class="metric-unit">%</text></text><view class="metric-trend"><text class="trend-text stable">稳定</text></view><view class="metric-status normal"><text>正常范围</text></view></view><view class="metric-card temperature"><view class="metric-header"><text class="metric-icon">🌡️</text><text class="metric-label">体温</text></view><text class="metric-value">36.5 <text class="metric-unit">°C</text></text><view class="metric-trend"><text class="trend-text stable">稳定</text></view><view class="metric-status normal"><text>正常范围</text></view></view></view><view class="chart-section"><view class="section-header"><text class="section-title">趋势图表</text><text class="section-link" bindtap="{{h}}">详细 ></text></view><view class="chart-card"><view class="chart-tabs"><view class="{{['chart-tab', i && 'active']}}" bindtap="{{j}}"><text>心率</text></view><view class="{{['chart-tab', k && 'active']}}" bindtap="{{l}}"><text>血压</text></view><view class="{{['chart-tab', m && 'active']}}" bindtap="{{n}}"><text>血氧</text></view></view><view class="chart-placeholder"><text class="chart-title">{{o}}</text><view class="chart-mock"><view class="chart-line"></view><view class="chart-points"><view wx:for="{{p}}" wx:for-item="point" wx:key="a" class="chart-point" style="{{'left:' + point.b + ';' + ('bottom:' + point.c)}}"></view></view></view><view class="chart-labels"><text>00:00</text><text>06:00</text><text>12:00</text><text>18:00</text><text>24:00</text></view></view></view></view><view class="records-section"><view class="section-header"><text class="section-title">健康记录</text><text class="section-link" bindtap="{{q}}">全部 ></text></view><view class="records-list"><view wx:for="{{r}}" wx:for-item="record" wx:key="g" class="record-item" bindtap="{{record.h}}"><view class="record-time"><text class="record-date">{{record.a}}</text><text class="record-hour">{{record.b}}</text></view><view class="record-content"><text class="record-title">{{record.c}}</text><text class="record-value">{{record.d}}</text><view class="{{['record-status', record.f]}}"><text>{{record.e}}</text></view></view><view class="record-arrow"><text class="iconfont icon-arrow-right">></text></view></view></view></view><view class="suggestions-section"><view class="section-header"><text class="section-title">健康建议</text></view><view class="suggestions-list"><view wx:for="{{s}}" wx:for-item="suggestion" wx:key="e" class="suggestion-item"><view class="{{['suggestion-icon', suggestion.b]}}"><text class="iconfont">{{suggestion.a}}</text></view><view class="suggestion-content"><text class="suggestion-title">{{suggestion.c}}</text><text class="suggestion-desc">{{suggestion.d}}</text></view></view></view></view></scroll-view></view>