"use strict";
const common_vendor = require("../../common/vendor.js");
const _sfc_main = {
  data() {
    return {
      activeTab: "all",
      messages: [
        {
          id: 1,
          type: "system",
          title: "系统通知",
          content: "您的健康数据已更新，请及时查看最新的健康报告。",
          time: new Date(Date.now() - 30 * 60 * 1e3),
          // 30分钟前
          isRead: false,
          actionText: "查看详情",
          avatar: null
        },
        {
          id: 2,
          type: "service",
          title: "服务预约确认",
          content: "您预约的营养配餐服务已确认，服务时间：明天12:00。",
          time: new Date(Date.now() - 2 * 60 * 60 * 1e3),
          // 2小时前
          isRead: false,
          actionText: "查看预约",
          avatar: null
        },
        {
          id: 3,
          type: "health",
          title: "健康提醒",
          content: "今日血压测量提醒：请在上午9点进行血压测量。",
          time: new Date(Date.now() - 4 * 60 * 60 * 1e3),
          // 4小时前
          isRead: true,
          actionText: "立即测量",
          avatar: null
        },
        {
          id: 4,
          type: "service",
          title: "护理服务评价",
          content: "您使用的专业护理服务已完成，请为本次服务进行评价。",
          time: new Date(Date.now() - 24 * 60 * 60 * 1e3),
          // 1天前
          isRead: true,
          actionText: "去评价",
          avatar: null
        },
        {
          id: 5,
          type: "system",
          title: "账户安全",
          content: "检测到您的账户在新设备上登录，如非本人操作请及时修改密码。",
          time: new Date(Date.now() - 2 * 24 * 60 * 60 * 1e3),
          // 2天前
          isRead: true,
          actionText: "查看详情",
          avatar: null
        },
        {
          id: 6,
          type: "health",
          title: "体检报告",
          content: "您的定期体检报告已生成，各项指标正常，请注意日常保健。",
          time: new Date(Date.now() - 3 * 24 * 60 * 60 * 1e3),
          // 3天前
          isRead: false,
          actionText: "查看报告",
          avatar: null
        }
      ]
    };
  },
  computed: {
    filteredMessages() {
      if (this.activeTab === "all") {
        return this.messages;
      }
      return this.messages.filter((message) => message.type === this.activeTab);
    },
    unreadCount() {
      const counts = {
        all: 0,
        system: 0,
        service: 0,
        health: 0
      };
      this.messages.forEach((message) => {
        if (!message.isRead) {
          counts.all++;
          counts[message.type]++;
        }
      });
      return counts;
    }
  },
  methods: {
    switchTab(tab) {
      this.activeTab = tab;
    },
    onMessageTap(message) {
      message.isRead = true;
      console.log("点击消息:", message.title);
      switch (message.type) {
        case "health":
          common_vendor.index.switchTab({
            url: "/pages/health/health"
          });
          break;
        case "service":
          common_vendor.index.switchTab({
            url: "/pages/services/services"
          });
          break;
        default:
          this.showMessageDetail(message);
          break;
      }
    },
    showMessageDetail(message) {
      common_vendor.index.showModal({
        title: message.title,
        content: message.content,
        showCancel: false,
        confirmText: "知道了"
      });
    },
    markAllRead() {
      this.messages.forEach((message) => {
        message.isRead = true;
      });
      common_vendor.index.showToast({
        title: "已全部标记为已读",
        icon: "success"
      });
    },
    getTypeIcon(type) {
      const icons = {
        system: "🔔",
        service: "🛎️",
        health: "❤️"
      };
      return icons[type] || "📧";
    },
    getTypeName(type) {
      const names = {
        system: "系统",
        service: "服务",
        health: "健康"
      };
      return names[type] || "消息";
    },
    formatTime(time) {
      const now = /* @__PURE__ */ new Date();
      const diff = now - time;
      const minutes = Math.floor(diff / (1e3 * 60));
      const hours = Math.floor(diff / (1e3 * 60 * 60));
      const days = Math.floor(diff / (1e3 * 60 * 60 * 24));
      if (minutes < 60) {
        return `${minutes}分钟前`;
      } else if (hours < 24) {
        return `${hours}小时前`;
      } else if (days < 7) {
        return `${days}天前`;
      } else {
        return time.toLocaleDateString();
      }
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: common_vendor.o((...args) => $options.markAllRead && $options.markAllRead(...args)),
    b: $options.unreadCount.all > 0
  }, $options.unreadCount.all > 0 ? {
    c: common_vendor.t($options.unreadCount.all)
  } : {}, {
    d: $data.activeTab === "all" ? 1 : "",
    e: common_vendor.o(($event) => $options.switchTab("all")),
    f: $options.unreadCount.system > 0
  }, $options.unreadCount.system > 0 ? {
    g: common_vendor.t($options.unreadCount.system)
  } : {}, {
    h: $data.activeTab === "system" ? 1 : "",
    i: common_vendor.o(($event) => $options.switchTab("system")),
    j: $options.unreadCount.service > 0
  }, $options.unreadCount.service > 0 ? {
    k: common_vendor.t($options.unreadCount.service)
  } : {}, {
    l: $data.activeTab === "service" ? 1 : "",
    m: common_vendor.o(($event) => $options.switchTab("service")),
    n: $options.unreadCount.health > 0
  }, $options.unreadCount.health > 0 ? {
    o: common_vendor.t($options.unreadCount.health)
  } : {}, {
    p: $data.activeTab === "health" ? 1 : "",
    q: common_vendor.o(($event) => $options.switchTab("health")),
    r: common_vendor.f($options.filteredMessages, (message, k0, i0) => {
      return common_vendor.e({
        a: message.avatar
      }, message.avatar ? {
        b: message.avatar
      } : {
        c: common_vendor.t($options.getTypeIcon(message.type)),
        d: common_vendor.n(message.type)
      }, {
        e: common_vendor.t(message.title),
        f: common_vendor.t($options.formatTime(message.time)),
        g: common_vendor.t(message.content),
        h: message.actionText
      }, message.actionText ? {
        i: common_vendor.t($options.getTypeName(message.type)),
        j: common_vendor.n(message.type),
        k: common_vendor.t(message.actionText)
      } : {}, {
        l: !message.isRead
      }, !message.isRead ? {} : {}, {
        m: message.id,
        n: !message.isRead ? 1 : "",
        o: common_vendor.o(($event) => $options.onMessageTap(message), message.id)
      });
    }),
    s: $options.filteredMessages.length === 0
  }, $options.filteredMessages.length === 0 ? {} : {});
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render]]);
wx.createPage(MiniProgramPage);
