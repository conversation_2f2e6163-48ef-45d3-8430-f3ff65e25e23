
.health-container {
	background-color: #f8f9fa;
	min-height: 100vh;
}

/* 导航栏 */
.nav-bar {
	height: 56px;
	background: #ffffff;
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 0 16px;
	border-bottom: 1px solid #f0f0f0;
}
.nav-title {
	font-size: 18px;
	font-weight: 600;
	color: #1f2937;
}
.nav-icon {
	width: 32px;
	height: 32px;
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
	background: #f3f4f6;
	color: #6b7280;
}

/* 主内容 */
.main-content {
	padding: 16px;
	height: calc(100vh - 56px);
}

/* 时间选择器 */
.time-selector {
	display: flex;
	background: white;
	border-radius: 12px;
	padding: 4px;
	margin-bottom: 16px;
	box-shadow: 0 2px 8px rgba(0,0,0,0.05);
}
.tab-item {
	flex: 1;
	padding: 12px 0;
	text-align: center;
	font-size: 14px;
	color: #6b7280;
	border-radius: 8px;
	transition: all 0.2s;
}
.tab-item.active {
	color: #2563eb;
	background: #eff6ff;
	font-weight: 500;
}

/* 健康指标网格 */
.metrics-grid {
	display: grid;
	grid-template-columns: 1fr 1fr;
	gap: 12px;
	margin-bottom: 24px;
}
.metric-card {
	background: white;
	border-radius: 16px;
	padding: 16px;
	box-shadow: 0 2px 8px rgba(0,0,0,0.05);
	border: 1px solid #f3f4f6;
}
.metric-header {
	display: flex;
	align-items: center;
	margin-bottom: 12px;
}
.metric-icon {
	font-size: 20px;
	margin-right: 8px;
}
.metric-label {
	font-size: 14px;
	color: #6b7280;
	font-weight: 500;
}
.metric-value {
	font-size: 24px;
	font-weight: 700;
	color: #1f2937;
	margin-bottom: 8px;
	display: block;
}
.metric-unit {
	font-size: 14px;
	font-weight: 400;
	color: #6b7280;
}
.metric-trend {
	margin-bottom: 8px;
}
.trend-text {
	font-size: 12px;
	font-weight: 500;
}
.trend-text.up {
	color: #10b981;
}
.trend-text.stable {
	color: #6b7280;
}
.metric-status {
	padding: 4px 8px;
	border-radius: 6px;
	font-size: 11px;
	font-weight: 500;
	display: inline-block;
}
.metric-status.normal {
	background: rgba(16, 185, 129, 0.1);
	color: #10b981;
}

/* 图表区域 */
.chart-section {
	margin-bottom: 24px;
}
.section-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 12px;
}
.section-title {
	font-size: 16px;
	font-weight: 600;
	color: #1f2937;
	position: relative;
	padding-left: 12px;
}
.section-title::before {
	content: '';
	position: absolute;
	left: 0;
	top: 50%;
	-webkit-transform: translateY(-50%);
	        transform: translateY(-50%);
	width: 4px;
	height: 14px;
	background: #2563eb;
	border-radius: 2px;
}
.section-link {
	font-size: 12px;
	color: #2563eb;
	font-weight: 500;
}
.chart-card {
	background: white;
	border-radius: 16px;
	padding: 16px;
	box-shadow: 0 2px 8px rgba(0,0,0,0.05);
	border: 1px solid #f3f4f6;
}
.chart-tabs {
	display: flex;
	margin-bottom: 16px;
	border-bottom: 1px solid #f3f4f6;
}
.chart-tab {
	padding: 8px 16px;
	font-size: 14px;
	color: #6b7280;
	border-bottom: 2px solid transparent;
	transition: all 0.2s;
}
.chart-tab.active {
	color: #2563eb;
	border-bottom-color: #2563eb;
	font-weight: 500;
}
.chart-placeholder {
	height: 200px;
	position: relative;
}
.chart-title {
	font-size: 14px;
	color: #6b7280;
	margin-bottom: 16px;
	display: block;
}
.chart-mock {
	height: 120px;
	position: relative;
	background: linear-gradient(to bottom, #f8fafc 0%, #f1f5f9 100%);
	border-radius: 8px;
	margin-bottom: 12px;
	overflow: hidden;
}
.chart-line {
	position: absolute;
	top: 20%;
	left: 10%;
	right: 10%;
	height: 2px;
	background: linear-gradient(to right, #2563eb, #3b82f6, #2563eb);
	border-radius: 1px;
}
.chart-points {
	position: absolute;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
}
.chart-point {
	position: absolute;
	width: 6px;
	height: 6px;
	background: #2563eb;
	border-radius: 50%;
	-webkit-transform: translate(-50%, 50%);
	        transform: translate(-50%, 50%);
	box-shadow: 0 0 0 2px white, 0 2px 4px rgba(37, 99, 235, 0.3);
}
.chart-labels {
	display: flex;
	justify-content: space-between;
	font-size: 12px;
	color: #9ca3af;
}

/* 健康记录 */
.records-section {
	margin-bottom: 24px;
}
.records-list {
	background: white;
	border-radius: 16px;
	overflow: hidden;
	box-shadow: 0 2px 8px rgba(0,0,0,0.05);
	border: 1px solid #f3f4f6;
}
.record-item {
	display: flex;
	align-items: center;
	padding: 16px;
	border-bottom: 1px solid #f3f4f6;
}
.record-item:last-child {
	border-bottom: none;
}
.record-time {
	display: flex;
	flex-direction: column;
	align-items: center;
	margin-right: 16px;
	min-width: 60px;
}
.record-date {
	font-size: 12px;
	color: #6b7280;
	font-weight: 500;
}
.record-hour {
	font-size: 11px;
	color: #9ca3af;
}
.record-content {
	flex: 1;
}
.record-title {
	font-size: 14px;
	font-weight: 500;
	color: #1f2937;
	margin-bottom: 4px;
	display: block;
}
.record-value {
	font-size: 16px;
	font-weight: 600;
	color: #2563eb;
	margin-bottom: 4px;
	display: block;
}
.record-status {
	padding: 2px 8px;
	border-radius: 6px;
	font-size: 11px;
	font-weight: 500;
	display: inline-block;
}
.record-status.normal {
	background: rgba(16, 185, 129, 0.1);
	color: #10b981;
}
.record-arrow {
	color: #d1d5db;
	font-size: 14px;
}

/* 健康建议 */
.suggestions-section {
	margin-bottom: 24px;
}
.suggestions-list {
	background: white;
	border-radius: 16px;
	overflow: hidden;
	box-shadow: 0 2px 8px rgba(0,0,0,0.05);
	border: 1px solid #f3f4f6;
}
.suggestion-item {
	display: flex;
	align-items: center;
	padding: 16px;
	border-bottom: 1px solid #f3f4f6;
}
.suggestion-item:last-child {
	border-bottom: none;
}
.suggestion-icon {
	width: 40px;
	height: 40px;
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
	margin-right: 12px;
	font-size: 18px;
}
.suggestion-icon.exercise {
	background: rgba(16, 185, 129, 0.1);
	color: #10b981;
}
.suggestion-icon.diet {
	background: rgba(245, 158, 11, 0.1);
	color: #f59e0b;
}
.suggestion-icon.sleep {
	background: rgba(139, 92, 246, 0.1);
	color: #8b5cf6;
}
.suggestion-content {
	flex: 1;
}
.suggestion-title {
	font-size: 14px;
	font-weight: 500;
	color: #1f2937;
	margin-bottom: 4px;
	display: block;
}
.suggestion-desc {
	font-size: 12px;
	color: #6b7280;
	line-height: 1.4;
}

/* 图标字体 */
.iconfont {
	font-family: 'iconfont';
}
.icon-settings::before { content: '⚙️';
}
.icon-arrow-right::before { content: '>';
}
