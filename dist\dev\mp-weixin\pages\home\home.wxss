
.home-container {
	background-color: #f8f9fa;
	min-height: 100vh;
}

/* 状态栏 */
.status-bar {
	height: 44px;
	background-color: #4f46e5;
}

/* 导航栏 */
.nav-bar {
	height: 64px;
	background: linear-gradient(135deg, #4f46e5, #818cf8);
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 0 20px;
	color: white;
	position: relative;
	box-shadow: 0 2px 10px rgba(79, 70, 229, 0.1);
}
.nav-title {
	font-size: 20px;
	font-weight: 600;
	letter-spacing: 0.5px;
}
.nav-actions {
	display: flex;
	gap: 16px;
}
.nav-icon {
	width: 36px;
	height: 36px;
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
	background: rgba(255, 255, 255, 0.15);
	color: white;
	position: relative;
}
.notification-badge {
	position: absolute;
	top: 0;
	right: 0;
	width: 8px;
	height: 8px;
	background-color: #ef4444;
	border-radius: 50%;
}

/* 主内容 */
.main-content {
	padding: 8px 16px 100px;
	height: calc(100vh - 108px);
}

/* 欢迎卡片 */
.welcome-card {
	background: linear-gradient(135deg, #4f46e5, #818cf8);
	border-radius: 16px;
	padding: 20px;
	color: white;
	margin-bottom: 20px;
	box-shadow: 0 10px 25px -5px rgba(79, 70, 229, 0.2);
}
.welcome-text {
	margin-bottom: 20px;
}
.welcome-greeting {
	font-size: 18px;
	font-weight: 600;
	display: block;
	margin-bottom: 4px;
	opacity: 0.9;
}
.welcome-date {
	font-size: 13px;
	opacity: 0.8;
	font-weight: 400;
}
.weather-info {
	display: flex;
	align-items: center;
	justify-content: space-between;
	background: rgba(255, 255, 255, 0.15);
	border-radius: 12px;
	padding: 12px 16px;
}
.weather-left {
	display: flex;
	align-items: center;
}
.weather-icon {
	font-size: 28px;
	margin-right: 12px;
}
.weather-temp {
	font-size: 24px;
	font-weight: 700;
}
.weather-details {
	text-align: right;
}
.weather-desc {
	font-size: 13px;
	margin-bottom: 4px;
	font-weight: 500;
	display: block;
}
.weather-extra {
	font-size: 11px;
	opacity: 0.9;
	display: flex;
	gap: 8px;
}

/* 通用区块样式 */
.section {
	margin-bottom: 24px;
}
.section-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 12px;
	padding: 0 4px;
}
.section-title {
	font-size: 16px;
	font-weight: 600;
	color: #1f2937;
	position: relative;
	padding-left: 12px;
}
.section-title::before {
	content: '';
	position: absolute;
	left: 0;
	top: 50%;
	-webkit-transform: translateY(-50%);
	        transform: translateY(-50%);
	width: 4px;
	height: 14px;
	background: #4f46e5;
	border-radius: 2px;
}
.section-link {
	font-size: 12px;
	color: #4f46e5;
	font-weight: 500;
}

/* 健康数据网格 */
.health-grid {
	display: grid;
	grid-template-columns: 1fr 1fr;
	gap: 12px;
}
.health-metric {
	padding: 16px;
	border-radius: 14px;
	color: white;
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	text-align: center;
	position: relative;
	overflow: hidden;
}
.health-metric.heart-rate {
	background: linear-gradient(135deg, #6366f1, #8b5cf6);
}
.health-metric.blood-oxygen {
	background: linear-gradient(135deg, #10b981, #34d399);
}
.health-metric.blood-pressure {
	background: linear-gradient(135deg, #f59e0b, #fbbf24);
}
.health-metric.temperature {
	background: linear-gradient(135deg, #ef4444, #f87171);
}
.metric-label {
	font-size: 12px;
	opacity: 0.9;
	font-weight: 500;
	letter-spacing: 0.3px;
}
.metric-value {
	font-size: 24px;
	font-weight: 700;
	margin: 4px 0;
}
.metric-trend {
	font-size: 12px;
	margin-top: 4px;
}
.metric-trend.up {
	color: #10b981;
}
.metric-trend.stable,
.metric-trend.normal {
	color: rgba(255, 255, 255, 0.9);
}

/* 服务网格 */
.service-grid {
	display: grid;
	grid-template-columns: repeat(4, 1fr);
	gap: 12px;
	padding: 0 4px;
}
.service-item {
	display: flex;
	flex-direction: column;
	align-items: center;
	padding: 16px 8px;
	border-radius: 12px;
	background: white;
	border: 1px solid #f3f4f6;
}
.service-icon {
	width: 44px;
	height: 44px;
	border-radius: 14px;
	display: flex;
	align-items: center;
	justify-content: center;
	margin-bottom: 8px;
	font-size: 20px;
	color: white;
	background: linear-gradient(135deg, #4f46e5, #818cf8);
	box-shadow: 0 4px 12px rgba(79, 70, 229, 0.2);
}
.service-label {
	font-size: 12px;
	font-weight: 500;
	color: #374151;
	text-align: center;
	line-height: 1.3;
}

/* 活动卡片 */
.activity-card {
	background: white;
	border-radius: 16px;
	padding: 16px;
	box-shadow: 0 4px 20px rgba(0, 0, 0, 0.03);
	border: 1px solid rgba(0, 0, 0, 0.03);
}
.activity-item {
	display: flex;
	padding: 14px 0;
	border-bottom: 1px solid #f3f4f6;
}
.activity-item:last-child {
	border-bottom: none;
}
.activity-time {
	font-size: 12px;
	color: #9ca3af;
	margin-right: 12px;
	min-width: 60px;
	font-weight: 500;
}
.activity-dot {
	width: 10px;
	height: 10px;
	border-radius: 50%;
	background: #4f46e5;
	margin-top: 6px;
	margin-right: 12px;
	flex-shrink: 0;
	position: relative;
}
.activity-dot::after {
	content: '';
	position: absolute;
	top: 50%;
	left: 50%;
	-webkit-transform: translate(-50%, -50%);
	        transform: translate(-50%, -50%);
	width: 16px;
	height: 16px;
	background: rgba(79, 70, 229, 0.1);
	border-radius: 50%;
	z-index: -1;
}
.activity-content {
	flex: 1;
}
.activity-title-row {
	display: flex;
	align-items: center;
	margin-bottom: 4px;
}
.activity-title {
	font-weight: 500;
	color: #1f2937;
	font-size: 14px;
	margin-right: 8px;
}
.activity-badge {
	padding: 2px 8px;
	border-radius: 12px;
	font-size: 11px;
	font-weight: 600;
}
.activity-badge.active {
	background: rgba(16, 185, 129, 0.1);
	color: #10b981;
}
.activity-badge.pending {
	background: rgba(245, 158, 11, 0.1);
	color: #f59e0b;
}
.badge-text {
	font-size: 11px;
}
.activity-desc {
	font-size: 12px;
	color: #6b7280;
	line-height: 1.4;
}

/* 紧急联系人卡片 */
.emergency-card {
	background: white;
	border-radius: 16px;
	padding: 16px;
	box-shadow: 0 4px 20px rgba(0, 0, 0, 0.03);
	border: 1px solid rgba(0, 0, 0, 0.03);
	display: flex;
	align-items: center;
	justify-content: space-between;
}
.emergency-info {
	display: flex;
	align-items: center;
}
.emergency-icon {
	width: 48px;
	height: 48px;
	border-radius: 50%;
	background: rgba(239, 68, 68, 0.1);
	display: flex;
	align-items: center;
	justify-content: center;
	margin-right: 12px;
	color: #ef4444;
	font-size: 20px;
}
.emergency-text {
	display: flex;
	flex-direction: column;
}
.emergency-title {
	font-weight: 500;
	color: #1f2937;
	font-size: 14px;
}
.emergency-desc {
	font-size: 12px;
	color: #6b7280;
}
.emergency-button {
	width: 40px;
	height: 40px;
	border-radius: 50%;
	background: #ef4444;
	color: white;
	display: flex;
	align-items: center;
	justify-content: center;
	font-size: 16px;
}

/* 悬浮按钮 */
.floating-action-button {
	position: fixed;
	bottom: 90px;
	right: 20px;
	width: 56px;
	height: 56px;
	border-radius: 50%;
	background: #4f46e5;
	color: white;
	display: flex;
	align-items: center;
	justify-content: center;
	box-shadow: 0 4px 20px rgba(79, 70, 229, 0.3);
	z-index: 30;
	font-size: 24px;
}

/* 图标字体 */
.iconfont {
	font-family: 'iconfont';
}
.icon-search::before { content: '🔍';
}
.icon-bell::before { content: '🔔';
}
.icon-utensils::before { content: '🍽️';
}
.icon-bath::before { content: '🛁';
}
.icon-hands::before { content: '🤲';
}
.icon-medical::before { content: '🏥';
}
.icon-tshirt::before { content: '👕';
}
.icon-broom::before { content: '🧹';
}
.icon-car::before { content: '🚗';
}
.icon-more::before { content: '⋯';
}
.icon-phone::before { content: '📞';
}
.icon-plus::before { content: '➕';
}
