"use strict";
const common_vendor = require("../../common/vendor.js");
const _sfc_main = {
  data() {
    return {
      searchKeyword: "",
      activeCategory: "all",
      sortBy: "default",
      serviceCategories: [
        { id: "all", name: "全部", icon: "📋" },
        { id: "dining", name: "餐饮", icon: "🍽️" },
        { id: "bathing", name: "助浴", icon: "🛁" },
        { id: "nursing", name: "护理", icon: "🤲" },
        { id: "medical", name: "医疗", icon: "🏥" },
        { id: "laundry", name: "洗衣", icon: "👕" },
        { id: "cleaning", name: "清洁", icon: "🧹" },
        { id: "transport", name: "出行", icon: "🚗" }
      ],
      recommendedServices: [
        {
          id: 1,
          name: "营养配餐",
          price: 35,
          rating: 4.8,
          image: "/static/services/dining1.jpg"
        },
        {
          id: 2,
          name: "专业护理",
          price: 80,
          rating: 4.9,
          image: "/static/services/nursing1.jpg"
        },
        {
          id: 3,
          name: "助浴服务",
          price: 60,
          rating: 4.7,
          image: "/static/services/bathing1.jpg"
        }
      ],
      allServices: [
        {
          id: 1,
          name: "营养配餐服务",
          description: "专业营养师配制的健康餐食，适合老年人营养需求",
          price: 35,
          rating: 4.8,
          reviewCount: 128,
          provider: "康养餐饮中心",
          category: "dining",
          status: "available",
          statusText: "可预约",
          image: "/static/services/dining1.jpg"
        },
        {
          id: 2,
          name: "专业护理服务",
          description: "持证护理员提供专业的日常护理和健康监护",
          price: 80,
          rating: 4.9,
          reviewCount: 95,
          provider: "爱心护理中心",
          category: "nursing",
          status: "available",
          statusText: "可预约",
          image: "/static/services/nursing1.jpg"
        },
        {
          id: 3,
          name: "助浴服务",
          description: "安全舒适的助浴服务，专业设备和护理人员",
          price: 60,
          rating: 4.7,
          reviewCount: 76,
          provider: "温馨助浴中心",
          category: "bathing",
          status: "available",
          statusText: "可预约",
          image: "/static/services/bathing1.jpg"
        },
        {
          id: 4,
          name: "健康体检",
          description: "定期健康检查，包含基础体检和专项检查",
          price: 120,
          rating: 4.6,
          reviewCount: 54,
          provider: "社区医疗中心",
          category: "medical",
          status: "busy",
          statusText: "预约满",
          image: "/static/services/medical1.jpg"
        }
      ],
      myBookings: [
        {
          id: 1,
          serviceName: "营养配餐",
          provider: "康养餐饮中心",
          date: "07-05",
          time: "12:00",
          status: "confirmed",
          statusText: "已确认"
        },
        {
          id: 2,
          serviceName: "专业护理",
          provider: "爱心护理中心",
          date: "07-06",
          time: "09:00",
          status: "pending",
          statusText: "待确认"
        }
      ]
    };
  },
  computed: {
    currentCategoryName() {
      const category = this.serviceCategories.find((cat) => cat.id === this.activeCategory);
      return category ? category.name + "服务" : "全部服务";
    },
    filteredServices() {
      let services = this.allServices;
      if (this.activeCategory !== "all") {
        services = services.filter((service) => service.category === this.activeCategory);
      }
      if (this.searchKeyword) {
        services = services.filter(
          (service) => service.name.includes(this.searchKeyword) || service.description.includes(this.searchKeyword)
        );
      }
      if (this.sortBy === "price") {
        services = services.sort((a, b) => a.price - b.price);
      } else if (this.sortBy === "rating") {
        services = services.sort((a, b) => b.rating - a.rating);
      }
      return services;
    }
  },
  methods: {
    onSearch() {
    },
    showFilter() {
    },
    selectCategory(categoryId) {
      this.activeCategory = categoryId;
    },
    setSortBy(sortType) {
      this.sortBy = sortType;
    },
    onServiceTap(service) {
      console.log("点击服务:", service.name);
    },
    bookService(service) {
      console.log("预约服务:", service.name);
      common_vendor.index.showToast({
        title: "预约成功",
        icon: "success"
      });
    },
    addToFavorites(service) {
      console.log("收藏服务:", service.name);
      common_vendor.index.showToast({
        title: "已收藏",
        icon: "success"
      });
    },
    viewAllRecommended() {
      console.log("查看全部推荐");
    },
    viewAllBookings() {
      console.log("查看全部预约");
    },
    onBookingTap(booking) {
      console.log("点击预约:", booking.serviceName);
    },
    cancelBooking(booking) {
      console.log("取消预约:", booking.serviceName);
    },
    contactProvider(booking) {
      console.log("联系服务商:", booking.provider);
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: common_vendor.o((...args) => $options.onSearch && $options.onSearch(...args)),
    b: common_vendor.o([($event) => $data.searchKeyword = $event.detail.value, (...args) => $options.onSearch && $options.onSearch(...args)]),
    c: $data.searchKeyword,
    d: common_vendor.o((...args) => $options.showFilter && $options.showFilter(...args)),
    e: common_vendor.f($data.serviceCategories, (category, k0, i0) => {
      return {
        a: common_vendor.t(category.icon),
        b: common_vendor.t(category.name),
        c: $data.activeCategory === category.id ? 1 : "",
        d: category.id,
        e: common_vendor.o(($event) => $options.selectCategory(category.id), category.id)
      };
    }),
    f: $data.activeCategory === "all"
  }, $data.activeCategory === "all" ? {
    g: common_vendor.o((...args) => $options.viewAllRecommended && $options.viewAllRecommended(...args)),
    h: common_vendor.f($data.recommendedServices, (service, k0, i0) => {
      return {
        a: service.image,
        b: common_vendor.t(service.name),
        c: common_vendor.t(service.price),
        d: common_vendor.t(service.rating),
        e: service.id,
        f: common_vendor.o(($event) => $options.onServiceTap(service), service.id)
      };
    })
  } : {}, {
    i: common_vendor.t($options.currentCategoryName),
    j: $data.sortBy === "default" ? 1 : "",
    k: common_vendor.o(($event) => $options.setSortBy("default")),
    l: $data.sortBy === "price" ? 1 : "",
    m: common_vendor.o(($event) => $options.setSortBy("price")),
    n: $data.sortBy === "rating" ? 1 : "",
    o: common_vendor.o(($event) => $options.setSortBy("rating")),
    p: common_vendor.f($options.filteredServices, (service, k0, i0) => {
      return {
        a: service.image,
        b: common_vendor.t(service.name),
        c: common_vendor.t(service.statusText),
        d: common_vendor.n(service.status),
        e: common_vendor.t(service.description),
        f: common_vendor.t(service.price),
        g: common_vendor.t(service.rating),
        h: common_vendor.t(service.reviewCount),
        i: common_vendor.t(service.provider),
        j: common_vendor.o(($event) => $options.addToFavorites(service), service.id),
        k: common_vendor.o(($event) => $options.bookService(service), service.id),
        l: service.id,
        m: common_vendor.o(($event) => $options.onServiceTap(service), service.id)
      };
    }),
    q: common_vendor.o((...args) => $options.viewAllBookings && $options.viewAllBookings(...args)),
    r: common_vendor.f($data.myBookings, (booking, k0, i0) => {
      return common_vendor.e({
        a: common_vendor.t(booking.date),
        b: common_vendor.t(booking.time),
        c: common_vendor.t(booking.serviceName),
        d: common_vendor.t(booking.provider),
        e: common_vendor.t(booking.statusText),
        f: common_vendor.n(booking.status),
        g: booking.status === "pending"
      }, booking.status === "pending" ? {
        h: common_vendor.o(($event) => $options.cancelBooking(booking), booking.id)
      } : {}, {
        i: booking.status === "confirmed"
      }, booking.status === "confirmed" ? {
        j: common_vendor.o(($event) => $options.contactProvider(booking), booking.id)
      } : {}, {
        k: booking.id,
        l: common_vendor.o(($event) => $options.onBookingTap(booking), booking.id)
      });
    })
  });
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render]]);
wx.createPage(MiniProgramPage);
