"use strict";
const common_vendor = require("../../common/vendor.js");
const _sfc_main = {
  data() {
    return {
      activeTab: "today",
      activeChart: "heartRate",
      chartData: [
        { x: 10, y: 30 },
        { x: 25, y: 45 },
        { x: 40, y: 35 },
        { x: 55, y: 50 },
        { x: 70, y: 40 },
        { x: 85, y: 55 }
      ],
      healthRecords: [
        {
          date: "07-04",
          time: "09:30",
          title: "心率监测",
          value: "72 bpm",
          status: "normal",
          statusText: "正常"
        },
        {
          date: "07-04",
          time: "08:00",
          title: "血压测量",
          value: "125/80 mmHg",
          status: "normal",
          statusText: "正常"
        },
        {
          date: "07-03",
          time: "20:30",
          title: "体温检测",
          value: "36.5°C",
          status: "normal",
          statusText: "正常"
        },
        {
          date: "07-03",
          time: "15:00",
          title: "血氧监测",
          value: "98%",
          status: "normal",
          statusText: "正常"
        }
      ],
      healthSuggestions: [
        {
          type: "exercise",
          icon: "🏃",
          title: "适量运动",
          description: "建议每天进行30分钟轻度运动，如散步或太极"
        },
        {
          type: "diet",
          icon: "🥗",
          title: "饮食调理",
          description: "保持低盐低脂饮食，多吃蔬菜水果"
        },
        {
          type: "sleep",
          icon: "😴",
          title: "规律作息",
          description: "保证每天7-8小时睡眠，晚上10点前入睡"
        }
      ]
    };
  },
  computed: {
    chartTitle() {
      const titles = {
        heartRate: "心率趋势 (bpm)",
        bloodPressure: "血压趋势 (mmHg)",
        bloodOxygen: "血氧趋势 (%)"
      };
      return titles[this.activeChart] || "数据趋势";
    }
  },
  methods: {
    switchTab(tab) {
      this.activeTab = tab;
    },
    switchChart(chart) {
      this.activeChart = chart;
    },
    onSettings() {
      console.log("设置");
    },
    viewDetailChart() {
      console.log("查看详细图表");
    },
    viewAllRecords() {
      console.log("查看全部记录");
    },
    onRecordTap(record) {
      console.log("点击记录:", record.title);
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return {
    a: common_vendor.o((...args) => $options.onSettings && $options.onSettings(...args)),
    b: $data.activeTab === "today" ? 1 : "",
    c: common_vendor.o(($event) => $options.switchTab("today")),
    d: $data.activeTab === "week" ? 1 : "",
    e: common_vendor.o(($event) => $options.switchTab("week")),
    f: $data.activeTab === "month" ? 1 : "",
    g: common_vendor.o(($event) => $options.switchTab("month")),
    h: common_vendor.o((...args) => $options.viewDetailChart && $options.viewDetailChart(...args)),
    i: $data.activeChart === "heartRate" ? 1 : "",
    j: common_vendor.o(($event) => $options.switchChart("heartRate")),
    k: $data.activeChart === "bloodPressure" ? 1 : "",
    l: common_vendor.o(($event) => $options.switchChart("bloodPressure")),
    m: $data.activeChart === "bloodOxygen" ? 1 : "",
    n: common_vendor.o(($event) => $options.switchChart("bloodOxygen")),
    o: common_vendor.t($options.chartTitle),
    p: common_vendor.f($data.chartData, (point, index, i0) => {
      return {
        a: index,
        b: point.x + "%",
        c: point.y + "%"
      };
    }),
    q: common_vendor.o((...args) => $options.viewAllRecords && $options.viewAllRecords(...args)),
    r: common_vendor.f($data.healthRecords, (record, index, i0) => {
      return {
        a: common_vendor.t(record.date),
        b: common_vendor.t(record.time),
        c: common_vendor.t(record.title),
        d: common_vendor.t(record.value),
        e: common_vendor.t(record.statusText),
        f: common_vendor.n(record.status),
        g: index,
        h: common_vendor.o(($event) => $options.onRecordTap(record), index)
      };
    }),
    s: common_vendor.f($data.healthSuggestions, (suggestion, index, i0) => {
      return {
        a: common_vendor.t(suggestion.icon),
        b: common_vendor.n(suggestion.type),
        c: common_vendor.t(suggestion.title),
        d: common_vendor.t(suggestion.description),
        e: index
      };
    })
  };
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render]]);
wx.createPage(MiniProgramPage);
