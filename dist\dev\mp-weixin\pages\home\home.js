"use strict";
const common_vendor = require("../../common/vendor.js");
const _sfc_main = {
  data() {
    return {
      hasNotification: true,
      currentDate: "",
      services: [
        { name: "餐饮服务", icon: "icon-utensils" },
        { name: "助浴服务", icon: "icon-bath" },
        { name: "护理服务", icon: "icon-hands" },
        { name: "医疗服务", icon: "icon-medical" },
        { name: "洗衣服务", icon: "icon-tshirt" },
        { name: "清洁服务", icon: "icon-broom" },
        { name: "出行服务", icon: "icon-car" },
        { name: "更多服务", icon: "icon-more" }
      ],
      todayActivities: [
        {
          time: "09:30",
          title: "晨间运动",
          description: "在护理人员陪同下进行30分钟晨间散步，请穿舒适运动鞋",
          badge: "进行中",
          status: "active"
        },
        {
          time: "12:00",
          title: "午餐时间",
          description: "低盐低脂营养餐（预约号：A123）",
          badge: "",
          status: ""
        },
        {
          time: "15:00",
          title: "健康检查",
          description: "定期健康检查，请到医疗室进行血压、血糖等常规检查",
          badge: "待确认",
          status: "pending"
        },
        {
          time: "16:30",
          title: "手工活动",
          description: "社区活动中心 - 手工制作课程",
          badge: "",
          status: ""
        }
      ]
    };
  },
  onLoad() {
    this.updateCurrentDate();
  },
  methods: {
    updateCurrentDate() {
      const now = /* @__PURE__ */ new Date();
      const year = now.getFullYear();
      const month = now.getMonth() + 1;
      const date = now.getDate();
      const weekdays = ["星期日", "星期一", "星期二", "星期三", "星期四", "星期五", "星期六"];
      const weekday = weekdays[now.getDay()];
      this.currentDate = `${year}年${month}月${date}日 ${weekday}`;
    },
    onSearch() {
      console.log("搜索");
    },
    onNotification() {
      common_vendor.index.navigateTo({
        url: "/pages/messages/messages"
      });
    },
    goToHealth() {
      common_vendor.index.switchTab({
        url: "/pages/health/health"
      });
    },
    goToServices() {
      common_vendor.index.switchTab({
        url: "/pages/services/services"
      });
    },
    goToProfile() {
      common_vendor.index.navigateTo({
        url: "/pages/profile/profile"
      });
    },
    onServiceTap(service) {
      console.log("点击服务:", service.name);
      common_vendor.index.navigateTo({
        url: "/pages/services/services"
      });
    },
    onActivityTap(activity) {
      console.log("点击活动:", activity.title);
    },
    viewAllActivities() {
      console.log("查看全部活动");
    },
    makeEmergencyCall() {
      common_vendor.index.showModal({
        title: "紧急呼叫",
        content: "确定要拨打紧急联系电话吗？",
        success: (res) => {
          if (res.confirm) {
            common_vendor.index.makePhoneCall({
              phoneNumber: "120"
            });
          }
        }
      });
    },
    onFabTap() {
      console.log("悬浮按钮点击");
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: common_vendor.o((...args) => $options.onSearch && $options.onSearch(...args)),
    b: $data.hasNotification
  }, $data.hasNotification ? {} : {}, {
    c: common_vendor.o((...args) => $options.onNotification && $options.onNotification(...args)),
    d: common_vendor.t($data.currentDate),
    e: common_vendor.o((...args) => $options.goToHealth && $options.goToHealth(...args)),
    f: common_vendor.o((...args) => $options.goToServices && $options.goToServices(...args)),
    g: common_vendor.f($data.services, (service, index, i0) => {
      return {
        a: common_vendor.n(service.icon),
        b: common_vendor.t(service.name),
        c: index,
        d: common_vendor.o(($event) => $options.onServiceTap(service), index)
      };
    }),
    h: common_vendor.o((...args) => $options.viewAllActivities && $options.viewAllActivities(...args)),
    i: common_vendor.f($data.todayActivities, (activity, index, i0) => {
      return common_vendor.e({
        a: common_vendor.t(activity.time),
        b: common_vendor.t(activity.title),
        c: activity.badge
      }, activity.badge ? {
        d: common_vendor.t(activity.badge),
        e: common_vendor.n(activity.status)
      } : {}, {
        f: common_vendor.t(activity.description),
        g: index,
        h: common_vendor.o(($event) => $options.onActivityTap(activity), index)
      });
    }),
    j: common_vendor.o((...args) => $options.goToProfile && $options.goToProfile(...args)),
    k: common_vendor.o((...args) => $options.makeEmergencyCall && $options.makeEmergencyCall(...args)),
    l: common_vendor.o((...args) => $options.onFabTap && $options.onFabTap(...args))
  });
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render]]);
wx.createPage(MiniProgramPage);
