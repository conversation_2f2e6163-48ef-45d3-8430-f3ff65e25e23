
.messages-container {
	background-color: #f8f9fa;
	min-height: 100vh;
}

/* 导航栏 */
.nav-bar {
	height: 56px;
	background: #ffffff;
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 0 16px;
	border-bottom: 1px solid #f0f0f0;
}
.nav-title {
	font-size: 18px;
	font-weight: 600;
	color: #1f2937;
}
.nav-icon {
	width: 32px;
	height: 32px;
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
	background: #f3f4f6;
	color: #6b7280;
}

/* 主内容 */
.main-content {
	height: calc(100vh - 56px);
}

/* 消息分类标签 */
.message-tabs {
	display: flex;
	background: white;
	padding: 0 16px;
	border-bottom: 1px solid #f0f0f0;
}
.tab-item {
	flex: 1;
	padding: 16px 0;
	text-align: center;
	font-size: 14px;
	color: #6b7280;
	position: relative;
	border-bottom: 2px solid transparent;
	transition: all 0.2s;
}
.tab-item.active {
	color: #2563eb;
	border-bottom-color: #2563eb;
	font-weight: 500;
}
.tab-badge {
	position: absolute;
	top: 8px;
	right: 20%;
	background: #ef4444;
	color: white;
	font-size: 10px;
	padding: 2px 6px;
	border-radius: 10px;
	min-width: 16px;
	height: 16px;
	display: flex;
	align-items: center;
	justify-content: center;
}

/* 消息列表 */
.messages-list {
	background: white;
}
.message-item {
	display: flex;
	padding: 16px;
	border-bottom: 1px solid #f3f4f6;
	transition: background 0.2s;
}
.message-item:last-child {
	border-bottom: none;
}
.message-item.unread {
	background: #fefefe;
}
.message-item.unread .message-title {
	font-weight: 600;
}
.message-avatar {
	width: 48px;
	height: 48px;
	margin-right: 12px;
	flex-shrink: 0;
}
.avatar-image {
	width: 100%;
	height: 100%;
	border-radius: 50%;
}
.avatar-icon {
	width: 100%;
	height: 100%;
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
	font-size: 20px;
}
.avatar-icon.system {
	background: rgba(59, 130, 246, 0.1);
	color: #3b82f6;
}
.avatar-icon.service {
	background: rgba(16, 185, 129, 0.1);
	color: #10b981;
}
.avatar-icon.health {
	background: rgba(239, 68, 68, 0.1);
	color: #ef4444;
}
.message-content {
	flex: 1;
	min-width: 0;
}
.message-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 4px;
}
.message-title {
	font-size: 14px;
	color: #1f2937;
	font-weight: 500;
}
.message-time {
	font-size: 12px;
	color: #9ca3af;
	flex-shrink: 0;
	margin-left: 8px;
}
.message-preview {
	font-size: 13px;
	color: #6b7280;
	line-height: 1.4;
	margin-bottom: 8px;
	display: -webkit-box;
	-webkit-line-clamp: 2;
	-webkit-box-orient: vertical;
	overflow: hidden;
}
.message-footer {
	display: flex;
	justify-content: space-between;
	align-items: center;
}
.message-type-badge {
	padding: 2px 8px;
	border-radius: 6px;
	font-size: 11px;
	font-weight: 500;
}
.message-type-badge.system {
	background: rgba(59, 130, 246, 0.1);
	color: #3b82f6;
}
.message-type-badge.service {
	background: rgba(16, 185, 129, 0.1);
	color: #10b981;
}
.message-type-badge.health {
	background: rgba(239, 68, 68, 0.1);
	color: #ef4444;
}
.message-action {
	font-size: 12px;
	color: #2563eb;
	font-weight: 500;
}
.message-indicator {
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	margin-left: 8px;
}
.unread-dot {
	width: 8px;
	height: 8px;
	background: #ef4444;
	border-radius: 50%;
	margin-bottom: 4px;
}
.message-arrow {
	color: #d1d5db;
	font-size: 14px;
}

/* 空状态 */
.empty-state {
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	padding: 80px 20px;
	text-align: center;
}
.empty-icon {
	font-size: 64px;
	margin-bottom: 16px;
	opacity: 0.5;
}
.empty-text {
	font-size: 16px;
	color: #6b7280;
	font-weight: 500;
	margin-bottom: 8px;
}
.empty-desc {
	font-size: 14px;
	color: #9ca3af;
}

/* 图标字体 */
/* 响应式适配 */
@media (max-width: 375px) {
.message-preview {
		-webkit-line-clamp: 1;
}
.message-footer {
		flex-direction: column;
		align-items: flex-start;
		gap: 4px;
}
}

/* 图标字体 */
.iconfont {
	font-family: 'iconfont';
}
.icon-check::before { content: '✓';
}
