"use strict";
const common_vendor = require("../../common/vendor.js");
const _sfc_main = {
  data() {
    return {
      userInfo: {
        name: "张爷爷",
        id: "ZY20250001",
        avatar: "",
        healthStatus: "good",
        joinDate: "2024年1月"
      },
      userStats: {
        totalServices: 28,
        healthRecords: 156,
        points: 1280,
        coupons: 3
      },
      pendingBookings: 2
    };
  },
  methods: {
    getHealthStatusText(status) {
      const statusMap = {
        excellent: "健康优秀",
        good: "健康良好",
        normal: "健康正常",
        attention: "需要关注"
      };
      return statusMap[status] || "健康正常";
    },
    editProfile() {
      console.log("编辑个人资料");
    },
    onSettings() {
      console.log("设置");
    },
    goToServices() {
      common_vendor.index.switchTab({
        url: "/pages/services/services"
      });
    },
    goToHealth() {
      common_vendor.index.switchTab({
        url: "/pages/health/health"
      });
    },
    viewPoints() {
      console.log("查看积分");
    },
    viewCoupons() {
      console.log("查看优惠券");
    },
    goToBookings() {
      console.log("我的预约");
    },
    goToFavorites() {
      console.log("我的收藏");
    },
    goToHistory() {
      console.log("服务历史");
    },
    goToHealthRecords() {
      console.log("健康档案");
    },
    goToMedicalHistory() {
      console.log("就医记录");
    },
    goToEmergencyContacts() {
      console.log("紧急联系人");
    },
    goToAccountSettings() {
      console.log("账户设置");
    },
    goToPrivacySettings() {
      console.log("隐私设置");
    },
    goToNotificationSettings() {
      console.log("通知设置");
    },
    goToHelp() {
      console.log("帮助中心");
    },
    contactSupport() {
      common_vendor.index.makePhoneCall({
        phoneNumber: "************"
      });
    },
    goToFeedback() {
      console.log("意见反馈");
    },
    goToAbout() {
      console.log("关于我们");
    },
    logout() {
      common_vendor.index.showModal({
        title: "确认退出",
        content: "确定要退出登录吗？",
        success: (res) => {
          if (res.confirm) {
            common_vendor.index.showToast({
              title: "已退出登录",
              icon: "success"
            });
          }
        }
      });
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: common_vendor.o((...args) => $options.onSettings && $options.onSettings(...args)),
    b: $data.userInfo.avatar
  }, $data.userInfo.avatar ? {
    c: $data.userInfo.avatar
  } : {
    d: common_vendor.t($data.userInfo.name.charAt(0))
  }, {
    e: common_vendor.t($data.userInfo.name),
    f: common_vendor.t($data.userInfo.id),
    g: common_vendor.t($options.getHealthStatusText($data.userInfo.healthStatus)),
    h: common_vendor.n($data.userInfo.healthStatus),
    i: common_vendor.t($data.userInfo.joinDate),
    j: common_vendor.o((...args) => $options.editProfile && $options.editProfile(...args)),
    k: common_vendor.t($data.userStats.totalServices),
    l: common_vendor.o((...args) => $options.goToServices && $options.goToServices(...args)),
    m: common_vendor.t($data.userStats.healthRecords),
    n: common_vendor.o((...args) => $options.goToHealth && $options.goToHealth(...args)),
    o: common_vendor.t($data.userStats.points),
    p: common_vendor.o((...args) => $options.viewPoints && $options.viewPoints(...args)),
    q: common_vendor.t($data.userStats.coupons),
    r: common_vendor.o((...args) => $options.viewCoupons && $options.viewCoupons(...args)),
    s: $data.pendingBookings > 0
  }, $data.pendingBookings > 0 ? {
    t: common_vendor.t($data.pendingBookings)
  } : {}, {
    v: common_vendor.o((...args) => $options.goToBookings && $options.goToBookings(...args)),
    w: common_vendor.o((...args) => $options.goToFavorites && $options.goToFavorites(...args)),
    x: common_vendor.o((...args) => $options.goToHistory && $options.goToHistory(...args)),
    y: common_vendor.o((...args) => $options.goToHealthRecords && $options.goToHealthRecords(...args)),
    z: common_vendor.o((...args) => $options.goToMedicalHistory && $options.goToMedicalHistory(...args)),
    A: common_vendor.o((...args) => $options.goToEmergencyContacts && $options.goToEmergencyContacts(...args)),
    B: common_vendor.o((...args) => $options.goToAccountSettings && $options.goToAccountSettings(...args)),
    C: common_vendor.o((...args) => $options.goToPrivacySettings && $options.goToPrivacySettings(...args)),
    D: common_vendor.o((...args) => $options.goToNotificationSettings && $options.goToNotificationSettings(...args)),
    E: common_vendor.o((...args) => $options.goToHelp && $options.goToHelp(...args)),
    F: common_vendor.o((...args) => $options.contactSupport && $options.contactSupport(...args)),
    G: common_vendor.o((...args) => $options.goToFeedback && $options.goToFeedback(...args)),
    H: common_vendor.o((...args) => $options.goToAbout && $options.goToAbout(...args)),
    I: common_vendor.o((...args) => $options.logout && $options.logout(...args))
  });
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render]]);
wx.createPage(MiniProgramPage);
