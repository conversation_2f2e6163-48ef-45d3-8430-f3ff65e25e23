
.profile-container {
	background-color: #f8f9fa;
	min-height: 100vh;
}

/* 导航栏 */
.nav-bar {
	height: 56px;
	background: linear-gradient(135deg, #4f46e5 0%, #7c3aed 100%);
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 0 16px;
	color: white;
}
.nav-title {
	font-size: 18px;
	font-weight: 600;
	color: white;
}
.nav-icon {
	width: 32px;
	height: 32px;
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
	background: rgba(255, 255, 255, 0.2);
	color: white;
}

/* 主内容 */
.main-content {
	height: calc(100vh - 56px);
}

/* 用户信息卡片 */
.user-card {
	background: white;
	padding: 20px 16px;
	display: flex;
	align-items: center;
	border-bottom: 1px solid #f3f4f6;
}
.user-avatar {
	width: 64px;
	height: 64px;
	margin-right: 16px;
	flex-shrink: 0;
}
.avatar-image {
	width: 100%;
	height: 100%;
	border-radius: 50%;
}
.avatar-placeholder {
	width: 100%;
	height: 100%;
	border-radius: 50%;
	background: linear-gradient(135deg, #4f46e5 0%, #7c3aed 100%);
	display: flex;
	align-items: center;
	justify-content: center;
}
.avatar-text {
	font-size: 24px;
	font-weight: 600;
	color: white;
}
.user-info {
	flex: 1;
	min-width: 0;
}
.user-name {
	font-size: 18px;
	font-weight: 600;
	color: #1f2937;
	margin-bottom: 4px;
}
.user-id {
	font-size: 12px;
	color: #9ca3af;
	margin-bottom: 8px;
}
.user-status {
	display: flex;
	align-items: center;
	gap: 12px;
}
.status-badge {
	padding: 2px 8px;
	border-radius: 6px;
	font-size: 11px;
	font-weight: 500;
}
.status-badge.excellent {
	background: rgba(16, 185, 129, 0.1);
	color: #10b981;
}
.status-badge.good {
	background: rgba(59, 130, 246, 0.1);
	color: #3b82f6;
}
.status-badge.normal {
	background: rgba(245, 158, 11, 0.1);
	color: #f59e0b;
}
.status-badge.attention {
	background: rgba(239, 68, 68, 0.1);
	color: #ef4444;
}
.join-date {
	font-size: 11px;
	color: #9ca3af;
}
.edit-button {
	width: 32px;
	height: 32px;
	border-radius: 50%;
	background: #f3f4f6;
	display: flex;
	align-items: center;
	justify-content: center;
	color: #6b7280;
	margin-left: 12px;
}

/* 快捷统计 */
.stats-section {
	background: white;
	padding: 16px;
	margin-bottom: 8px;
}
.stats-grid {
	display: grid;
	grid-template-columns: repeat(4, 1fr);
	gap: 16px;
}
.stat-item {
	text-align: center;
	padding: 12px 8px;
	border-radius: 8px;
	background: #f8f9fa;
	transition: all 0.2s;
}
.stat-item:active {
	background: #f3f4f6;
	-webkit-transform: translateY(1px);
	        transform: translateY(1px);
}
.stat-number {
	display: block;
	font-size: 20px;
	font-weight: 700;
	color: #4f46e5;
	margin-bottom: 4px;
}
.stat-label {
	font-size: 12px;
	color: #6b7280;
}

/* 功能菜单 */
.menu-section {
	background: white;
}
.menu-group {
	border-bottom: 8px solid #f8f9fa;
}
.menu-group:last-child {
	border-bottom: none;
}
.group-title {
	font-size: 14px;
	font-weight: 600;
	color: #6b7280;
	padding: 16px 16px 8px;
	background: #f8f9fa;
}
.menu-item {
	display: flex;
	align-items: center;
	padding: 16px;
	border-bottom: 1px solid #f8f9fa;
	transition: background 0.2s;
}
.menu-item:last-child {
	border-bottom: none;
}
.menu-item:active {
	background: #f8f9fa;
}
.menu-icon {
	width: 40px;
	height: 40px;
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
	margin-right: 12px;
	font-size: 18px;
}
.menu-icon.service {
	background: rgba(79, 70, 229, 0.1);
	color: #4f46e5;
}
.menu-icon.favorite {
	background: rgba(239, 68, 68, 0.1);
	color: #ef4444;
}
.menu-icon.history {
	background: rgba(107, 114, 128, 0.1);
	color: #6b7280;
}
.menu-icon.health {
	background: rgba(16, 185, 129, 0.1);
	color: #10b981;
}
.menu-icon.medical {
	background: rgba(59, 130, 246, 0.1);
	color: #3b82f6;
}
.menu-icon.emergency {
	background: rgba(239, 68, 68, 0.1);
	color: #ef4444;
}
.menu-icon.account {
	background: rgba(107, 114, 128, 0.1);
	color: #6b7280;
}
.menu-icon.privacy {
	background: rgba(245, 158, 11, 0.1);
	color: #f59e0b;
}
.menu-icon.notification {
	background: rgba(59, 130, 246, 0.1);
	color: #3b82f6;
}
.menu-icon.help {
	background: rgba(16, 185, 129, 0.1);
	color: #10b981;
}
.menu-icon.support {
	background: rgba(79, 70, 229, 0.1);
	color: #4f46e5;
}
.menu-icon.feedback {
	background: rgba(245, 158, 11, 0.1);
	color: #f59e0b;
}
.menu-icon.about {
	background: rgba(107, 114, 128, 0.1);
	color: #6b7280;
}
.menu-text {
	flex: 1;
	font-size: 15px;
	color: #1f2937;
}
.menu-badge {
	background: #ef4444;
	color: white;
	font-size: 10px;
	padding: 2px 6px;
	border-radius: 10px;
	min-width: 16px;
	height: 16px;
	display: flex;
	align-items: center;
	justify-content: center;
	margin-right: 8px;
}
.menu-arrow {
	color: #d1d5db;
	font-size: 14px;
}

/* 退出登录 */
.logout-section {
	padding: 24px 16px;
}
.logout-button {
	width: 100%;
	height: 48px;
	background: #ef4444;
	color: white;
	border: none;
	border-radius: 12px;
	font-size: 16px;
	font-weight: 600;
	transition: all 0.2s;
}
.logout-button:active {
	background: #dc2626;
	-webkit-transform: translateY(1px);
	        transform: translateY(1px);
}
