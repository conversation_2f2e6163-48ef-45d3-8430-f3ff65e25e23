<view class="services-container"><view class="nav-bar"><view class="nav-left"><text class="nav-title">服务预约</text></view><view class="nav-right"><view class="nav-icon" bindtap="{{a}}"><text class="iconfont icon-search">🔍</text></view></view></view><scroll-view class="main-content" scroll-y="true"><view class="search-bar"><view class="search-input"><text class="search-icon">🔍</text><input type="text" placeholder="搜索服务..." bindinput="{{b}}" value="{{c}}"/></view><view class="filter-button" bindtap="{{d}}"><text class="iconfont icon-filter">🔽</text></view></view><view class="categories-section"><view class="section-header"><text class="section-title">服务分类</text></view><scroll-view class="categories-scroll" scroll-x="true"><view class="categories-list"><view wx:for="{{e}}" wx:for-item="category" wx:key="d" class="{{['category-item', category.c && 'active']}}" bindtap="{{category.e}}"><view class="category-icon"><text class="iconfont">{{category.a}}</text></view><text class="category-name">{{category.b}}</text></view></view></scroll-view></view><view wx:if="{{f}}" class="recommended-section"><view class="section-header"><text class="section-title">推荐服务</text><text class="section-link" bindtap="{{g}}">全部 ></text></view><scroll-view class="recommended-scroll" scroll-x="true"><view class="recommended-list"><view wx:for="{{h}}" wx:for-item="service" wx:key="e" class="recommended-item" bindtap="{{service.f}}"><image class="service-image" src="{{service.a}}" mode="aspectFill"></image><view class="service-info"><text class="service-name">{{service.b}}</text><text class="service-price">¥{{service.c}}/次</text><view class="service-rating"><text class="rating-stars">⭐⭐⭐⭐⭐</text><text class="rating-score">{{service.d}}</text></view></view></view></view></scroll-view></view><view class="services-section"><view class="section-header"><text class="section-title">{{i}}</text><view class="sort-options"><text class="{{['sort-item', j && 'active']}}" bindtap="{{k}}">默认</text><text class="{{['sort-item', l && 'active']}}" bindtap="{{m}}">价格</text><text class="{{['sort-item', n && 'active']}}" bindtap="{{o}}">评分</text></view></view><view class="services-list"><view wx:for="{{p}}" wx:for-item="service" wx:key="l" class="service-card" bindtap="{{service.m}}"><image class="service-card-image" src="{{service.a}}" mode="aspectFill"></image><view class="service-card-content"><view class="service-card-header"><text class="service-card-name">{{service.b}}</text><view class="{{['service-card-badge', service.d]}}"><text>{{service.c}}</text></view></view><text class="service-card-desc">{{service.e}}</text><view class="service-card-details"><text class="service-card-price">¥{{service.f}}/次</text><view class="service-card-rating"><text class="rating-stars">⭐⭐⭐⭐⭐</text><text class="rating-score">{{service.g}}</text><text class="rating-count">({{service.h}})</text></view></view><view class="service-card-footer"><text class="service-provider">{{service.i}}</text><view class="service-card-actions"><button class="action-button secondary" catchtap="{{service.j}}">收藏</button><button class="action-button primary" catchtap="{{service.k}}">预约</button></view></view></view></view></view></view><view class="my-bookings-section"><view class="section-header"><text class="section-title">我的预约</text><text class="section-link" bindtap="{{q}}">全部 ></text></view><view class="bookings-list"><view wx:for="{{r}}" wx:for-item="booking" wx:key="k" class="booking-item" bindtap="{{booking.l}}"><view class="booking-time"><text class="booking-date">{{booking.a}}</text><text class="booking-hour">{{booking.b}}</text></view><view class="booking-content"><text class="booking-service">{{booking.c}}</text><text class="booking-provider">{{booking.d}}</text><view class="{{['booking-status', booking.f]}}"><text>{{booking.e}}</text></view></view><view class="booking-actions"><button wx:if="{{booking.g}}" class="booking-button" catchtap="{{booking.h}}">取消</button><button wx:if="{{booking.i}}" class="booking-button" catchtap="{{booking.j}}">联系</button></view></view></view></view></scroll-view></view>