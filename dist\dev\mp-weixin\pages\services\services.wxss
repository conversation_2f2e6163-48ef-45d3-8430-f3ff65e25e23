
.services-container {
	background-color: #f8f9fa;
	min-height: 100vh;
}

/* 导航栏 */
.nav-bar {
	height: 56px;
	background: linear-gradient(135deg, #4f46e5 0%, #7c3aed 100%);
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 0 16px;
	color: white;
}
.nav-title {
	font-size: 18px;
	font-weight: 600;
	color: white;
}
.nav-icon {
	width: 32px;
	height: 32px;
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
	background: rgba(255, 255, 255, 0.2);
	color: white;
}

/* 主内容 */
.main-content {
	height: calc(100vh - 56px);
}

/* 搜索栏 */
.search-bar {
	display: flex;
	padding: 16px;
	background: white;
	gap: 12px;
}
.search-input {
	flex: 1;
	height: 40px;
	background: #f3f4f6;
	border-radius: 20px;
	display: flex;
	align-items: center;
	padding: 0 16px;
	gap: 8px;
}
.search-icon {
	color: #9ca3af;
	font-size: 14px;
}
.search-input input {
	flex: 1;
	border: none;
	background: transparent;
	outline: none;
	font-size: 14px;
	color: #374151;
}
.filter-button {
	width: 40px;
	height: 40px;
	background: #4f46e5;
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
	color: white;
}

/* 服务分类 */
.categories-section {
	background: white;
	padding: 16px 0;
	border-bottom: 1px solid #f3f4f6;
}
.section-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 0 16px 12px;
}
.section-title {
	font-size: 16px;
	font-weight: 600;
	color: #1f2937;
}
.section-link {
	font-size: 14px;
	color: #4f46e5;
}
.categories-scroll {
	white-space: nowrap;
}
.categories-list {
	display: flex;
	padding: 0 16px;
	gap: 16px;
}
.category-item {
	display: flex;
	flex-direction: column;
	align-items: center;
	min-width: 60px;
	padding: 8px;
	border-radius: 12px;
	transition: all 0.2s;
}
.category-item.active {
	background: rgba(79, 70, 229, 0.1);
}
.category-icon {
	width: 40px;
	height: 40px;
	background: #f3f4f6;
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
	font-size: 18px;
	margin-bottom: 4px;
}
.category-item.active .category-icon {
	background: #4f46e5;
	color: white;
}
.category-name {
	font-size: 12px;
	color: #6b7280;
	text-align: center;
}
.category-item.active .category-name {
	color: #4f46e5;
	font-weight: 500;
}

/* 推荐服务 */
.recommended-section {
	background: white;
	padding: 16px 0;
	margin-bottom: 8px;
}
.recommended-scroll {
	white-space: nowrap;
}
.recommended-list {
	display: flex;
	padding: 0 16px;
	gap: 12px;
}
.recommended-item {
	width: 160px;
	background: white;
	border-radius: 12px;
	box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
	overflow: hidden;
	border: 1px solid #f3f4f6;
}
.service-image {
	width: 100%;
	height: 100px;
	background: #f3f4f6;
}
.service-info {
	padding: 12px;
}
.service-name {
	font-size: 14px;
	font-weight: 500;
	color: #1f2937;
	margin-bottom: 4px;
}
.service-price {
	font-size: 16px;
	font-weight: 600;
	color: #ef4444;
	margin-bottom: 4px;
}
.service-rating {
	display: flex;
	align-items: center;
	gap: 4px;
}
.rating-stars {
	font-size: 10px;
	color: #fbbf24;
}
.rating-score {
	font-size: 12px;
	color: #6b7280;
}

/* 服务列表 */
.services-section {
	background: white;
	padding: 16px 0;
}
.sort-options {
	display: flex;
	gap: 16px;
}
.sort-item {
	font-size: 14px;
	color: #6b7280;
	padding: 4px 0;
	border-bottom: 2px solid transparent;
	transition: all 0.2s;
}
.sort-item.active {
	color: #4f46e5;
	border-bottom-color: #4f46e5;
	font-weight: 500;
}
.services-list {
	padding: 0 16px;
}
.service-card {
	display: flex;
	background: white;
	border-radius: 12px;
	margin-bottom: 12px;
	box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
	overflow: hidden;
	border: 1px solid #f3f4f6;
}
.service-card-image {
	width: 100px;
	height: 100px;
	background: #f3f4f6;
	flex-shrink: 0;
}
.service-card-content {
	flex: 1;
	padding: 12px;
	display: flex;
	flex-direction: column;
}
.service-card-header {
	display: flex;
	justify-content: space-between;
	align-items: flex-start;
	margin-bottom: 4px;
}
.service-card-name {
	font-size: 16px;
	font-weight: 600;
	color: #1f2937;
	flex: 1;
}
.service-card-badge {
	padding: 2px 8px;
	border-radius: 6px;
	font-size: 11px;
	font-weight: 500;
	margin-left: 8px;
}
.service-card-badge.available {
	background: rgba(16, 185, 129, 0.1);
	color: #10b981;
}
.service-card-badge.busy {
	background: rgba(239, 68, 68, 0.1);
	color: #ef4444;
}
.service-card-desc {
	font-size: 13px;
	color: #6b7280;
	line-height: 1.4;
	margin-bottom: 8px;
	flex: 1;
}
.service-card-details {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 8px;
}
.service-card-price {
	font-size: 18px;
	font-weight: 600;
	color: #ef4444;
}
.service-card-rating {
	display: flex;
	align-items: center;
	gap: 4px;
}
.rating-count {
	font-size: 12px;
	color: #9ca3af;
}
.service-card-footer {
	display: flex;
	justify-content: space-between;
	align-items: center;
}
.service-provider {
	font-size: 12px;
	color: #6b7280;
}
.service-card-actions {
	display: flex;
	gap: 8px;
}
.action-button {
	padding: 6px 12px;
	border-radius: 6px;
	font-size: 12px;
	font-weight: 500;
	border: none;
	outline: none;
	transition: all 0.2s;
}
.action-button.primary {
	background: #4f46e5;
	color: white;
}
.action-button.secondary {
	background: #f3f4f6;
	color: #6b7280;
}

/* 我的预约 */
.my-bookings-section {
	background: white;
	padding: 16px 0;
	margin-top: 8px;
}
.bookings-list {
	padding: 0 16px;
}
.booking-item {
	display: flex;
	align-items: center;
	padding: 12px;
	background: #f8f9fa;
	border-radius: 8px;
	margin-bottom: 8px;
}
.booking-time {
	width: 60px;
	text-align: center;
	margin-right: 12px;
}
.booking-date {
	font-size: 12px;
	color: #6b7280;
	display: block;
}
.booking-hour {
	font-size: 14px;
	font-weight: 600;
	color: #1f2937;
}
.booking-content {
	flex: 1;
}
.booking-service {
	font-size: 14px;
	font-weight: 500;
	color: #1f2937;
	margin-bottom: 2px;
}
.booking-provider {
	font-size: 12px;
	color: #6b7280;
	margin-bottom: 4px;
}
.booking-status {
	padding: 2px 6px;
	border-radius: 4px;
	font-size: 10px;
	font-weight: 500;
	display: inline-block;
}
.booking-status.confirmed {
	background: rgba(16, 185, 129, 0.1);
	color: #10b981;
}
.booking-status.pending {
	background: rgba(245, 158, 11, 0.1);
	color: #f59e0b;
}
.booking-actions {
	margin-left: 8px;
}
.booking-button {
	padding: 4px 8px;
	border-radius: 4px;
	font-size: 11px;
	background: #4f46e5;
	color: white;
	border: none;
	outline: none;
}
