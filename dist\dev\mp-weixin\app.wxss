/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 智享颐年主色调 - 基于原型图设计 */
/* 扩展颜色系统 */
/* 中性色彩 */
/* 文字基本颜色 */
/* 扩展文字颜色 */
/* 背景颜色 */
/* 扩展背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius - 更新为现代化圆角 */
/* 扩展圆角系统 */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 扩展字体大小系统 */
/* 字体粗细 */
/* 行高 */
/* 阴影系统 */
/* 组件尺寸 */
/* 动画系统 */
/* 文章场景相关 - 更新为新的设计规范 */
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 智享颐年主色调 - 基于原型图设计 */
/* 扩展颜色系统 */
/* 中性色彩 */
/* 文字基本颜色 */
/* 扩展文字颜色 */
/* 背景颜色 */
/* 扩展背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius - 更新为现代化圆角 */
/* 扩展圆角系统 */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 扩展字体大小系统 */
/* 字体粗细 */
/* 行高 */
/* 阴影系统 */
/* 组件尺寸 */
/* 动画系统 */
/* 文章场景相关 - 更新为新的设计规范 */
/* 导入全局样式 */
/**
 * 智享颐年 - 通用组件样式
 * 基于原型图设计的通用样式类
 */
/* ==================== 通用布局类 ==================== */
.container {
  padding: 0 16px;
}
.flex {
  display: flex;
}
.flex-center {
  display: flex;
  align-items: center;
  justify-content: center;
}
.flex-between {
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.flex-column {
  display: flex;
  flex-direction: column;
}
.flex-1 {
  flex: 1;
}
/* ==================== 卡片样式 ==================== */
.card {
  background: #ffffff;
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.03);
  overflow: hidden;
  border: 1px solid rgba(0, 0, 0, 0.03);
}
.card-padding {
  padding: 16px;
}
.card-header {
  padding: 16px 16px 0;
}
.card-body {
  padding: 0 16px;
}
.card-footer {
  padding: 0 16px 16px;
}
/* ==================== 按钮样式 ==================== */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 0 16px;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 500;
  text-align: center;
  transition: all 0.2s ease;
  border: none;
  outline: none;
  cursor: pointer;
}
.btn-primary {
  background: #4f46e5;
  color: #ffffff;
  height: 40px;
}
.btn-primary:active {
  background: #3730a3;
  -webkit-transform: translateY(1px);
          transform: translateY(1px);
}
.btn-secondary {
  background: #f3f4f6;
  color: #374151;
  height: 40px;
}
.btn-secondary:active {
  background: #e5e7eb;
  -webkit-transform: translateY(1px);
          transform: translateY(1px);
}
.btn-success {
  background: #10b981;
  color: #ffffff;
  height: 40px;
}
.btn-success:active {
  background: #059669;
  -webkit-transform: translateY(1px);
          transform: translateY(1px);
}
.btn-warning {
  background: #f59e0b;
  color: #ffffff;
  height: 40px;
}
.btn-warning:active {
  background: #d97706;
  -webkit-transform: translateY(1px);
          transform: translateY(1px);
}
.btn-danger {
  background: #ef4444;
  color: #ffffff;
  height: 40px;
}
.btn-danger:active {
  background: #dc2626;
  -webkit-transform: translateY(1px);
          transform: translateY(1px);
}
.btn-sm {
  height: 32px;
  padding: 0 12px;
  font-size: 12px;
}
.btn-lg {
  height: 48px;
  padding: 0 24px;
  font-size: 16px;
}
.btn-block {
  width: 100%;
}
.btn-round {
  border-radius: 20px;
}
.btn-circle {
  border-radius: 50%;
  width: 40px;
  height: 40px;
  padding: 0;
}
/* ==================== 徽章样式 ==================== */
.badge {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 11px;
  font-weight: 600;
  line-height: 1.4;
}
.badge-primary {
  background: rgba(79, 70, 229, 0.1);
  color: #4f46e5;
}
.badge-success {
  background: rgba(16, 185, 129, 0.1);
  color: #10b981;
}
.badge-warning {
  background: rgba(245, 158, 11, 0.1);
  color: #f59e0b;
}
.badge-danger {
  background: rgba(239, 68, 68, 0.1);
  color: #ef4444;
}
.badge-info {
  background: rgba(59, 130, 246, 0.1);
  color: #3b82f6;
}
.badge-gray {
  background: rgba(107, 114, 128, 0.1);
  color: #6b7280;
}
/* ==================== 文字样式 ==================== */
.text-xs {
  font-size: 10px;
}
.text-sm {
  font-size: 12px;
}
.text-base {
  font-size: 14px;
}
.text-lg {
  font-size: 16px;
}
.text-xl {
  font-size: 18px;
}
.text-2xl {
  font-size: 20px;
}
.text-3xl {
  font-size: 24px;
}
.text-primary {
  color: #1f2937;
}
.text-secondary {
  color: #6b7280;
}
.text-tertiary {
  color: #9ca3af;
}
.text-success {
  color: #10b981;
}
.text-warning {
  color: #f59e0b;
}
.text-danger {
  color: #ef4444;
}
.text-info {
  color: #3b82f6;
}
.text-white {
  color: #ffffff;
}
.text-center {
  text-align: center;
}
.text-left {
  text-align: left;
}
.text-right {
  text-align: right;
}
.font-light {
  font-weight: 300;
}
.font-normal {
  font-weight: 400;
}
.font-medium {
  font-weight: 500;
}
.font-semibold {
  font-weight: 600;
}
.font-bold {
  font-weight: 700;
}
/* ==================== 间距样式 ==================== */
.m-0 {
  margin: 0;
}
.m-1 {
  margin: 4px;
}
.m-2 {
  margin: 8px;
}
.m-3 {
  margin: 12px;
}
.m-4 {
  margin: 16px;
}
.m-5 {
  margin: 20px;
}
.m-6 {
  margin: 24px;
}
.mt-0 {
  margin-top: 0;
}
.mt-1 {
  margin-top: 4px;
}
.mt-2 {
  margin-top: 8px;
}
.mt-3 {
  margin-top: 12px;
}
.mt-4 {
  margin-top: 16px;
}
.mt-5 {
  margin-top: 20px;
}
.mt-6 {
  margin-top: 24px;
}
.mb-0 {
  margin-bottom: 0;
}
.mb-1 {
  margin-bottom: 4px;
}
.mb-2 {
  margin-bottom: 8px;
}
.mb-3 {
  margin-bottom: 12px;
}
.mb-4 {
  margin-bottom: 16px;
}
.mb-5 {
  margin-bottom: 20px;
}
.mb-6 {
  margin-bottom: 24px;
}
.ml-0 {
  margin-left: 0;
}
.ml-1 {
  margin-left: 4px;
}
.ml-2 {
  margin-left: 8px;
}
.ml-3 {
  margin-left: 12px;
}
.ml-4 {
  margin-left: 16px;
}
.ml-5 {
  margin-left: 20px;
}
.ml-6 {
  margin-left: 24px;
}
.mr-0 {
  margin-right: 0;
}
.mr-1 {
  margin-right: 4px;
}
.mr-2 {
  margin-right: 8px;
}
.mr-3 {
  margin-right: 12px;
}
.mr-4 {
  margin-right: 16px;
}
.mr-5 {
  margin-right: 20px;
}
.mr-6 {
  margin-right: 24px;
}
.p-0 {
  padding: 0;
}
.p-1 {
  padding: 4px;
}
.p-2 {
  padding: 8px;
}
.p-3 {
  padding: 12px;
}
.p-4 {
  padding: 16px;
}
.p-5 {
  padding: 20px;
}
.p-6 {
  padding: 24px;
}
.pt-0 {
  padding-top: 0;
}
.pt-1 {
  padding-top: 4px;
}
.pt-2 {
  padding-top: 8px;
}
.pt-3 {
  padding-top: 12px;
}
.pt-4 {
  padding-top: 16px;
}
.pt-5 {
  padding-top: 20px;
}
.pt-6 {
  padding-top: 24px;
}
.pb-0 {
  padding-bottom: 0;
}
.pb-1 {
  padding-bottom: 4px;
}
.pb-2 {
  padding-bottom: 8px;
}
.pb-3 {
  padding-bottom: 12px;
}
.pb-4 {
  padding-bottom: 16px;
}
.pb-5 {
  padding-bottom: 20px;
}
.pb-6 {
  padding-bottom: 24px;
}
.pl-0 {
  padding-left: 0;
}
.pl-1 {
  padding-left: 4px;
}
.pl-2 {
  padding-left: 8px;
}
.pl-3 {
  padding-left: 12px;
}
.pl-4 {
  padding-left: 16px;
}
.pl-5 {
  padding-left: 20px;
}
.pl-6 {
  padding-left: 24px;
}
.pr-0 {
  padding-right: 0;
}
.pr-1 {
  padding-right: 4px;
}
.pr-2 {
  padding-right: 8px;
}
.pr-3 {
  padding-right: 12px;
}
.pr-4 {
  padding-right: 16px;
}
.pr-5 {
  padding-right: 20px;
}
.pr-6 {
  padding-right: 24px;
}
/* 全局基础样式 */
page {
  background-color: #f8f9fa;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", "PingFang SC", "Hiragino Sans GB", "Microsoft YaHei", "Helvetica Neue", Helvetica, Arial, sans-serif;
  font-size: 14px;
  line-height: 1.5;
  color: #1f2937;
}
/* 重置默认样式 */
* {
  box-sizing: border-box;
}
/* 滚动条样式 */
::-webkit-scrollbar {
  width: 0;
  background: transparent;
}
/* 通用动画 */
.fade-in {
  -webkit-animation: fadeIn 0.3s ease-in-out;
          animation: fadeIn 0.3s ease-in-out;
}
@-webkit-keyframes fadeIn {
from {
    opacity: 0;
    -webkit-transform: translateY(10px);
            transform: translateY(10px);
}
to {
    opacity: 1;
    -webkit-transform: translateY(0);
            transform: translateY(0);
}
}
@keyframes fadeIn {
from {
    opacity: 0;
    -webkit-transform: translateY(10px);
            transform: translateY(10px);
}
to {
    opacity: 1;
    -webkit-transform: translateY(0);
            transform: translateY(0);
}
}
.slide-up {
  -webkit-animation: slideUp 0.3s ease-out;
          animation: slideUp 0.3s ease-out;
}
@-webkit-keyframes slideUp {
from {
    -webkit-transform: translateY(100%);
            transform: translateY(100%);
}
to {
    -webkit-transform: translateY(0);
            transform: translateY(0);
}
}
@keyframes slideUp {
from {
    -webkit-transform: translateY(100%);
            transform: translateY(100%);
}
to {
    -webkit-transform: translateY(0);
            transform: translateY(0);
}
}page{--status-bar-height:25px;--top-window-height:0px;--window-top:0px;--window-bottom:0px;--window-left:0px;--window-right:0px;--window-magin:0px}[data-c-h="true"]{display: none !important;}